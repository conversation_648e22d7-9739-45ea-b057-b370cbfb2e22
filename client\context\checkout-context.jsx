"use client";

import { createContext, useState, useContext } from "react";
import { useAuth } from "@/context/auth-context";
import { useCart } from "@/context/cart-context";
import { ordersAPI, sslAPI } from "@/lib/api";
import { toast } from "sonner";

const CheckoutContext = createContext(null);

export const CheckoutProvider = ({ children }) => {
  const { user } = useAuth();
  const { clearCart } = useCart();

  // Checkout state
  const [checkoutData, setCheckoutData] = useState(null);
  const [checkoutMode, setCheckoutMode] = useState("cart"); // 'cart' or 'direct'
  const [loading, setLoading] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [error, setError] = useState(null);

  // Order state
  const [currentOrder, setCurrentOrder] = useState(null);
  const [pendingOrder, setPendingOrder] = useState(null);

  // Payment state
  const [paymentData, setPaymentData] = useState(null);
  const [paymentStatus, setPaymentStatus] = useState(null); // 'pending', 'processing', 'completed', 'failed'

  /**
   * Initialize checkout with pending orders from database
   */
  const initializeCheckout = async (eventId = null) => {
    try {
      console.log(currentOrder);
      setLoading(true);
      setError(null);

      if (!user) {
        throw new Error("User must be logged in to checkout");
      }

      let pendingOrdersResponse;

      if (eventId) {
        // Get pending orders for specific event
        pendingOrdersResponse = await ordersAPI.getUserPendingOrdersByEvent(
          eventId
        );
        setCheckoutMode("event-specific");
      } else {
        // Get all pending orders (cart-based checkout)
        pendingOrdersResponse = await ordersAPI.getUserPendingOrders();
        setCheckoutMode("cart");
      }

      if (!pendingOrdersResponse.success) {
        throw new Error(
          pendingOrdersResponse.message || "Failed to fetch pending orders"
        );
      }

      const pendingOrders = pendingOrdersResponse.data.orders;

      if (!pendingOrders || pendingOrders.length === 0) {
        throw new Error("No pending orders found");
      }

      // Transform pending orders to checkout format
      const checkoutItems = [];
      let totalAmount = 0;

      for (const order of pendingOrders) {
        for (const orderItem of order.orderitems) {
          const item = {
            order_id: order.order_id,
            order_item_id: orderItem.order_item_id,
            ticket_type_id: orderItem.ticket_type_id,
            quantity: orderItem.quantity,
            price: parseFloat(orderItem.unit_price),
            ticketType: orderItem.tickettypes.name,
            eventId: orderItem.tickettypes.events.event_id,
            eventTitle: orderItem.tickettypes.events.title,
            eventDate: orderItem.tickettypes.events.start_date,
            eventVenue: orderItem.tickettypes.events.venue || "TBA",
          };

          checkoutItems.push(item);
          totalAmount += item.price * item.quantity;
        }
      }

      setCheckoutData({
        items: checkoutItems,
        total: totalAmount,
        eventId,
        orders: pendingOrders,
        attendeeInfo: null,
      });
      // console.log("Checkout items:", checkoutItems);

      console.log(pendingOrders[0]);

      // Store the first order as current order for payment processing
      setCurrentOrder(pendingOrders[0]);
      // if (pendingOrders.length > 0) {
      // }
      console.log(currentOrder);
    } catch (error) {
      console.error("Error initializing checkout:", error);
      setError(error.message);
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Process payment using SSL Commerce
   */
  const processPayment = async () => {
    if (!user) {
      toast.error("Please login to continue");
      return { success: false, message: "Authentication required" };
    }

    if (!checkoutData || !currentOrder) {
      toast.error("No checkout data available");
      return { success: false, message: "No checkout data" };
    }

    try {
      setPaymentLoading(true);
      setPaymentStatus("processing");
      setError(null);

      // Use the current order ID from pending orders
      // const orderId = currentOrder.order_id;

      // Initiate SSL payment
      const paymentResult = await sslAPI.initiatePayment(currentOrder);

      if (paymentResult.success && paymentResult.data?.url) {
        setPaymentData(paymentResult.data);
        setPaymentStatus("pending");

        // Redirect to payment gateway
        window.location.href = paymentResult.data.url;

        return { success: true, data: paymentResult.data };
      } else {
        throw new Error(paymentResult.message || "Failed to initiate payment");
      }
    } catch (error) {
      console.error("Payment processing error:", error);
      setError(error.message);
      setPaymentStatus("failed");
      toast.error(error.message || "Payment failed. Please try again.");
      return { success: false, message: error.message };
    } finally {
      setPaymentLoading(false);
    }
  };

  /**
   * Handle payment success callback
   */
  const handlePaymentSuccess = async (transactionData) => {
    try {
      setLoading(true);
      setPaymentStatus("processing");

      // Validate payment with SSL
      const validationResult = await sslAPI.validatePayment(transactionData);

      if (validationResult.success) {
        setPaymentStatus("completed");

        // Clear cart for all checkout modes since we're using pending orders
        await clearCart();

        toast.success("Payment successful! Your tickets are being generated.");
        return { success: true };
      } else {
        throw new Error("Payment validation failed");
      }
    } catch (error) {
      console.error("Payment success handling error:", error);
      setError(error.message);
      setPaymentStatus("failed");
      toast.error("Payment validation failed");
      return { success: false, message: error.message };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle payment failure
   */
  const handlePaymentFailure = (errorData) => {
    setPaymentStatus("failed");
    setError(errorData?.message || "Payment failed");
    toast.error(errorData?.message || "Payment failed. Please try again.");
  };

  /**
   * Reset checkout state
   */
  const resetCheckout = () => {
    setCheckoutData(null);
    setCheckoutMode("cart");
    // setCurrentOrder(null);
    setPendingOrder(null);
    setPaymentData(null);
    setPaymentStatus(null);
    setError(null);
  };

  /**
   * Get checkout summary
   */
  const getCheckoutSummary = () => {
    if (!checkoutData) return null;

    const subtotal = checkoutData.total;
    const organizerFees = subtotal * 0.05; // 5%
    const serviceFees = subtotal * 0.1; // 10%
    const totalAmount = subtotal; // Fees disabled for now

    return {
      subtotal,
      organizerFees,
      serviceFees,
      totalAmount,
      itemCount: checkoutData.items.reduce(
        (count, item) => count + item.quantity,
        0
      ),
    };
  };

  /**
   * Validate checkout data
   */
  const validateCheckout = () => {
    if (!user) {
      return { valid: false, message: "Please login to continue" };
    }

    if (
      !checkoutData ||
      !checkoutData.items ||
      checkoutData.items.length === 0
    ) {
      return { valid: false, message: "No items in checkout" };
    }

    if (!currentOrder) {
      return { valid: false, message: "No pending order found" };
    }

    return { valid: true };
  };

  const value = {
    // State
    checkoutData,
    checkoutMode,
    loading,
    paymentLoading,
    error,
    currentOrder,
    pendingOrder,
    paymentData,
    paymentStatus,

    // Actions
    initializeCheckout,
    processPayment,
    handlePaymentSuccess,
    handlePaymentFailure,
    resetCheckout,

    // Utilities
    getCheckoutSummary,
    validateCheckout,
  };

  return (
    <CheckoutContext.Provider value={value}>
      {children}
    </CheckoutContext.Provider>
  );
};

export const useCheckout = () => {
  const context = useContext(CheckoutContext);
  if (!context) {
    throw new Error("useCheckout must be used within a CheckoutProvider");
  }
  return context;
};
